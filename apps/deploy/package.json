{"name": "@libra/deploy", "version": "1.0.0", "description": "Queue-based deployment service for Libra platform", "scripts": {"dev": "wrangler dev --port 3008 --persist-to=../web/.wrangler/state", "deploy": "wrangler deploy --minify", "deploy:dev": "wrangler deploy --env development --minify", "deploy:prod": "wrangler deploy --env production --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "typecheck": "tsc --noEmit", "update": "bun update", "test": "vitest"}, "dependencies": {"@libra/auth": "*", "@libra/common": "*", "@libra/db": "*", "@libra/middleware": "*", "@libra/sandbox": "*", "@libra/templates": "*", "@libra/typescript-config": "*", "hono": "^4.8.12", "@scalar/hono-api-reference": "^0.9.13", "@hono/zod-openapi": "^0.19.10", "zod": "^4.0.14", "drizzle-orm": "^0.44.4"}, "devDependencies": {"wrangler": "^4.27.0", "@types/node": "^24.1.0", "typescript": "^5.9.2", "vitest": "^3.2.4", "@vitest/coverage-v8": "^3.2.4"}, "keywords": ["cloudflare-workers", "deployment", "queue", "libra", "hono"], "license": "AGPL-3.0-only"}