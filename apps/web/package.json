{"name": "libra-core", "version": "1.0.0", "private": true, "type": "module", "scripts": {"prebuild": "paraglide-js compile --project ./project.inlang --outdir ./paraglide", "build": "paraglide-js compile --project ./project.inlang --outdir ./paraglide && dotenv -e ../../.env -- next build", "clean": "rm -rf ", "dev": "bun with-env next dev --turbo -p 3000", "test": "bun with-env vitest", "test:watch": "bun with-env vitest watch", "with-env": "dotenv -e ../../.env --", "payload": "PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload", "generate:schema": "PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload generate:db-schema", "migrate:create": "PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload migrate:create", "migrate": "PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload migrate", "db:update": "PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload generate:db-schema && PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload migrate:create && PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload migrate", "generate:importmap": "PAYLOAD_CONFIG_PATH=./payload.config.ts bun with-env payload generate:importmap", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "fix": "bun knif", "deploy": "paraglide-js compile --project ./project.inlang --outdir ./paraglide && opennextjs-cloudflare build && opennextjs-cloudflare deploy -- --keep-vars", "machine-translate": "inlang machine translate --project project.inlang", "update": "bun update", "analyze": "ANALYZE=true bun run build"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.0", "@ai-sdk/azure": "^2.0.0", "@ai-sdk/xai": "^2.0.0", "@git-diff-view/file": "^0.0.30", "@git-diff-view/react": "^0.0.30", "@hookform/resolvers": "^5.2.1", "@libra/api": "*", "@libra/auth": "*", "@libra/better-auth-cloudflare": "*", "@libra/better-auth-stripe": "*", "@libra/common": "*", "@libra/db": "*", "@libra/email": "*", "@libra/sandbox": "*", "@libra/shikicode": "*", "@libra/templates": "*", "@libra/ui": "*", "@lottiefiles/dotlottie-react": "^0.13.5", "@marsidev/react-turnstile": "^1.2.0", "@openrouter/ai-sdk-provider": "^1.0.0-beta.6", "@shikijs/transformers": "^3.9.1", "@tanstack/react-query": "^5.84.1", "@tanstack/react-table": "^8.21.3", "@tsparticles/engine": "^3.9.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.9.0", "ai": "^5.0.0", "date-fns": "^4.1.0", "diff": "^8.0.2", "fast-xml-parser": "^5.2.5", "file-saver": "^2.0.5", "jszip": "^3.10.1", "motion": "^12.23.12", "next": "15.3.5", "nextjs-toploader": "^3.8.16", "posthog-js": "^1.258.5", "posthog-node": "^5.6.0", "react": "^19.1.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "remark-gfm": "^4.0.1", "shiki": "^3.9.1", "superjson": "^2.2.2", "zustand": "^5.0.7"}, "devDependencies": {"@types/diff": "^8.0.0", "@tanstack/react-query-devtools": "^5.84.1", "@inlang/paraglide-js": "^2.2.0", "@inlang/cli": "^3.0.12", "@types/file-saver": "^2.0.7", "@next/bundle-analyzer": "15.4.2"}}